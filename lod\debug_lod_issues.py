#!/usr/bin/env python3
"""
LOD可见性问题调试脚本

用于排查以下问题：
1. 相机离得远时块不显示
2. 相机很近时也不显示LOD Tile
3. 只有进入bounding box才显示
4. 远处显示了低LOD，靠近后反而消失

使用方法：
1. 在Isaac Sim中打开你的场景
2. 运行此脚本
3. 移动相机到不同位置观察输出
"""

import omni.usd
from pxr import Usd, UsdGeom, Gf
import sys
import os

# 添加lod模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lod_scheduler import LODScheduler, TilesetLODManager, BoundingBox
from lod_config import get_default_lod_config

def debug_current_lod_state():
    """调试当前LOD状态"""
    print("=" * 60)
    print("LOD可见性问题调试工具")
    print("=" * 60)
    
    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD Stage")
        return
        
    print("✅ 成功获取USD Stage")
    
    # 创建LOD调度器
    try:
        config = get_default_lod_config()
        scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
        print("✅ 成功创建LOD调度器")
    except Exception as e:
        print(f"❌ 创建LOD调度器失败: {e}")
        return
        
    # 运行调试分析
    try:
        debug_info = scheduler.debug_lod_visibility_issues(verbose=True)
        
        print("\n" + "=" * 60)
        print("问题诊断")
        print("=" * 60)
        
        # 分析可能的问题
        issues_found = []
        
        # 问题1：相机在边界框内但距离计算为0
        if debug_info.get("camera_inside_bounds") and debug_info.get("sse_distance", 0) < 1.0:
            issues_found.append("🔍 发现问题1: 相机在边界框内，SSE距离过小可能导致计算问题")
            
        # 问题2：距离计算异常
        center_dist = debug_info.get("center_distance", 0)
        sse_dist = debug_info.get("sse_distance", 0)
        if abs(center_dist - sse_dist) > center_dist * 0.5:
            issues_found.append(f"🔍 发现问题2: 中心距离({center_dist:.1f}m)与SSE距离({sse_dist:.1f}m)差异较大")
            
        # 问题3：不在视锥体内
        if not debug_info.get("in_frustum", True):
            issues_found.append("🔍 发现问题3: 场景不在相机视锥体内，可能被错误剔除")
            
        # 问题4：LOD选择边界问题
        selected_lod = debug_info.get("selected_lod")
        distance_ranges = debug_info.get("distance_ranges", {})
        current_distance = debug_info.get("sse_distance", 0)
        
        # 检查是否在LOD切换边界附近
        for lod_name, (min_dist, max_dist) in distance_ranges.items():
            if lod_name == selected_lod:
                # 检查是否在边界的5%范围内
                range_size = max_dist - min_dist if max_dist != float('inf') else min_dist
                boundary_tolerance = range_size * 0.05
                
                if (current_distance - min_dist) < boundary_tolerance:
                    issues_found.append(f"🔍 发现问题4: 距离({current_distance:.1f}m)接近{lod_name} LOD下边界({min_dist:.1f}m)，可能出现切换不稳定")
                elif max_dist != float('inf') and (max_dist - current_distance) < boundary_tolerance:
                    issues_found.append(f"🔍 发现问题4: 距离({current_distance:.1f}m)接近{lod_name} LOD上边界({max_dist:.1f}m)，可能出现切换不稳定")
                    
        if issues_found:
            print("\n🚨 发现以下潜在问题:")
            for i, issue in enumerate(issues_found, 1):
                print(f"{i}. {issue}")
        else:
            print("\n✅ 未发现明显问题，LOD系统配置看起来正常")
            
        print("\n" + "=" * 60)
        print("建议解决方案")
        print("=" * 60)
        
        if debug_info.get("camera_inside_bounds"):
            print("💡 建议1: 相机在边界框内时，尝试移动相机到边界框外测试")
            print("💡 建议2: 检查距离计算逻辑，确保内部距离计算合理")
            
        if not debug_info.get("in_frustum", True):
            print("💡 建议3: 检查视锥体剔除逻辑，可能过于严格")
            print("💡 建议4: 尝试增大FOV或调整near/far平面")
            
        if selected_lod:
            print(f"💡 建议5: 当前应该显示 {selected_lod} LOD，检查对应的Prim是否存在且可见")
            
        print("\n" + "=" * 60)
        print("实时监控命令")
        print("=" * 60)
        print("移动相机后，再次运行以下命令查看变化：")
        print("debug_current_lod_state()")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_distance_calculation():
    """测试距离计算的准确性"""
    print("\n" + "=" * 60)
    print("距离计算测试")
    print("=" * 60)
    
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("❌ 错误：无法获取USD Stage")
        return
        
    config = get_default_lod_config()
    scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
    
    camera_pos = scheduler.get_camera_position()
    if not camera_pos:
        print("❌ 无法获取相机位置")
        return
        
    # 创建测试边界框
    test_cases = [
        # 相机在外部的情况
        BoundingBox(Gf.Vec3f(100, 100, 100), Gf.Vec3f(200, 200, 200)),
        # 相机可能在内部的情况  
        BoundingBox(Gf.Vec3f(-100, -100, -100), Gf.Vec3f(100, 100, 100)),
        # 小边界框
        BoundingBox(Gf.Vec3f(-1, -1, -1), Gf.Vec3f(1, 1, 1)),
    ]
    
    print(f"相机位置: {camera_pos}")
    
    for i, bbox in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}:")
        print(f"  边界框: {bbox.min_point} 到 {bbox.max_point}")
        print(f"  中心: {bbox.center}")
        print(f"  尺寸: {bbox.size}")
        
        # 检查相机是否在内部
        inside = bbox.contains_point(camera_pos)
        print(f"  相机在内部: {'是' if inside else '否'}")
        
        # 计算不同的距离
        center_dist = scheduler.calculate_distance_to_camera(bbox, camera_pos)
        sse_dist = scheduler.calculate_distance_to_bounding_sphere(
            camera_pos, bbox.center, bbox.size
        )
        
        print(f"  中心距离: {center_dist:.2f}m")
        print(f"  SSE距离: {sse_dist:.2f}m")
        
        # 计算SSE
        for lod_name, geometric_error in scheduler.lod_geometric_errors.items():
            sse = scheduler.calculate_sse(geometric_error, sse_dist)
            print(f"  {lod_name} SSE: {sse:.2f}px")

if __name__ == "__main__":
    print("请在Isaac Sim的Script Editor中运行以下函数：")
    print("1. debug_current_lod_state() - 调试当前LOD状态")
    print("2. test_distance_calculation() - 测试距离计算")
