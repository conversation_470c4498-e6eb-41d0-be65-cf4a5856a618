"""
LOD配置文件
用于管理LOD调度系统的各种参数和配置
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional
import json
import os
import math

class LODLevel(Enum):
    """LOD级别枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

@dataclass
class OctreeConfig:
    """八叉树配置类"""
    max_depth: int = 8  # 最大深度
    min_area_threshold: float = 100.0  # 最小面积阈值
    min_volume_threshold: float = 1000.0  # 最小体积阈值

class CentralizedLODConfig:
    """中心化LOD配置系统"""

    def __init__(self, tileset_path: Optional[str] = None):
        """
        初始化中心化LOD配置

        Args:
            tileset_path: tileset.json文件路径，如果提供则从中读取geometricError配置
        """
        self.tileset_path = tileset_path

        # 默认的LOD级别到几何误差的映射
        # 这些值会被tileset.json中的实际值覆盖
        self._default_geometric_errors = {
            32: "VeryLow",      # geometricError >= 32 -> Low LOD
            16: "Low",   # 16 <= geometricError < 32 -> Medium LOD
            8: "Low",    # 8 <= geometricError < 16 -> Medium LOD
            4: "Medium",    # 4 <= geometricError < 8 -> Medium LOD
            2: "Medium",      # 2 <= geometricError < 4 -> High LOD
            1: "High",      # geometricError < 2 -> High LOD
        }

        # SSE配置
        self.maximum_screen_space_error = 64.0  # 最大可接受屏幕像素误差阈值
        self.screen_width = 1920  # 视口宽度（像素）
        self.screen_height = 1080  # 视口高度（像素）
        self.horizontal_fov = 60.0  # 水平视野角（度）

        # 相机配置
        self.camera_path = "/World/Camera"

        # 八叉树配置
        self.octree_config = OctreeConfig()

        # 从tileset.json加载实际的几何误差配置
        self._geometric_error_to_lod = {}
        self._lod_to_geometric_errors = {}
        if tileset_path:
            self._load_geometric_errors_from_tileset(tileset_path)
        else:
            self._use_default_geometric_errors()

    def _load_geometric_errors_from_tileset(self, tileset_path: str):
        """从tileset.json文件中加载几何误差配置"""
        try:
            if not os.path.exists(tileset_path):
                print(f"Warning: Tileset file not found: {tileset_path}, using default config")
                self._use_default_geometric_errors()
                return

            with open(tileset_path, 'r', encoding='utf-8') as f:
                tileset_data = json.load(f)

            # 递归收集所有的geometricError值
            geometric_errors = set()
            self._collect_geometric_errors(tileset_data.get('root', {}), geometric_errors)

            # 根据收集到的geometricError值建立映射
            sorted_errors = sorted(geometric_errors, reverse=True)  # 从大到小排序

            self._geometric_error_to_lod = {}
            self._lod_to_geometric_errors = {"High": [], "Medium": [], "Low": [], "VeryLow": []}

            for error in sorted_errors:
                if error > 16:
                    lod_level = "VeryLow"
                elif error >= 8:
                    lod_level = "Low"
                elif error >= 2:
                    lod_level = "Medium"
                else:
                    lod_level = "High"

                self._geometric_error_to_lod[error] = lod_level
                self._lod_to_geometric_errors[lod_level].append(error)

            print(f"Loaded geometric errors from tileset: {sorted_errors}")
            print(f"LOD mapping: {self._geometric_error_to_lod}")

        except Exception as e:
            print(f"Error loading geometric errors from tileset: {e}")
            self._use_default_geometric_errors()

    def _collect_geometric_errors(self, tile_data: dict, errors: set):
        """递归收集tileset中的所有geometricError值"""
        if 'geometricError' in tile_data:
            errors.add(tile_data['geometricError'])

        # 递归处理子tiles
        for child in tile_data.get('children', []):
            self._collect_geometric_errors(child, errors)

    def _use_default_geometric_errors(self):
        """使用默认的几何误差配置"""
        self._geometric_error_to_lod = self._default_geometric_errors.copy()
        self._lod_to_geometric_errors = {
            "High": [1],
            "Medium": [2, 4],
            "Low": [8, 16],
            "VeryLow": [32]
        }
    
    def get_lod_level_from_geometric_error(self, geometric_error: float) -> str:
        """根据几何误差获取LOD级别"""
        # 找到最接近的几何误差值
        closest_error = min(self._geometric_error_to_lod.keys(),
                           key=lambda x: abs(x - geometric_error))
        return self._geometric_error_to_lod[closest_error]

    def get_lod_mapping(self) -> dict:
        """获取几何误差到LOD级别的完整映射"""
        return self._geometric_error_to_lod.copy()

    def save_config(self, file_path: str):
        """保存配置到文件"""
        config_data = {
            "tileset_path": self.tileset_path,
            "maximum_screen_space_error": self.maximum_screen_space_error,
            "screen_width": self.screen_width,
            "screen_height": self.screen_height,
            "horizontal_fov": self.horizontal_fov,
            "camera_path": self.camera_path,
            "octree_config": {
                "max_depth": self.octree_config.max_depth,
                "min_area_threshold": self.octree_config.min_area_threshold,
                "min_volume_threshold": self.octree_config.min_volume_threshold
            },
            "geometric_error_to_lod": self._geometric_error_to_lod,
            "lod_to_geometric_errors": self._lod_to_geometric_errors
        }

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

        print(f"Configuration saved to {file_path}")

    def load_config(self, file_path: str):
        """从文件加载配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            self.tileset_path = config_data.get("tileset_path", self.tileset_path)
            self.maximum_screen_space_error = config_data.get("maximum_screen_space_error", self.maximum_screen_space_error)
            self.screen_width = config_data.get("screen_width", self.screen_width)
            self.screen_height = config_data.get("screen_height", self.screen_height)
            self.horizontal_fov = config_data.get("horizontal_fov", self.horizontal_fov)
            self.camera_path = config_data.get("camera_path", self.camera_path)

            octree_config = config_data.get("octree_config", {})
            self.octree_config.max_depth = octree_config.get("max_depth", self.octree_config.max_depth)
            self.octree_config.min_area_threshold = octree_config.get("min_area_threshold", self.octree_config.min_area_threshold)
            self.octree_config.min_volume_threshold = octree_config.get("min_volume_threshold", self.octree_config.min_volume_threshold)

            self._geometric_error_to_lod = config_data.get("geometric_error_to_lod", self._geometric_error_to_lod)
            self._lod_to_geometric_errors = config_data.get("lod_to_geometric_errors", self._lod_to_geometric_errors)

            print(f"Configuration loaded from {file_path}")

        except Exception as e:
            print(f"Error loading configuration from {file_path}: {e}")

# 创建全局默认配置实例
DEFAULT_LOD_CONFIG = CentralizedLODConfig()

# 便捷函数
def get_default_lod_config() -> CentralizedLODConfig:
    """获取默认LOD配置"""
    return DEFAULT_LOD_CONFIG

def create_lod_config_from_tileset(tileset_path: str) -> CentralizedLODConfig:
    """从tileset文件创建LOD配置"""
    return CentralizedLODConfig(tileset_path=tileset_path)

@dataclass
class CameraConfig:
    """相机配置类"""
    fov: float = 60.0  # 视场角（度）
    screen_width: int = 1920  # 屏幕宽度
    screen_height: int = 1080  # 屏幕高度
    near_plane: float = 0.1  # 近平面
    far_plane: float = 1000.0  # 远平面

def main():
    save_path = "E:/wanleqi/isaacsim-python-scripts/lod/lod_config.json"
    config = CentralizedLODConfig("E:/wanleqi/isaacsim-python-scripts/lod/tileset_data/florenz_village/hierarchy_low/tileset_hierarchy_low.json")
    config.save_config(save_path)

if __name__ == "__main__":
    main()